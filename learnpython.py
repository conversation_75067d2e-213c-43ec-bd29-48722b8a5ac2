import tkinter as tk

root = tk.Tk()
root.title("Phone-Style Calculator")
root.geometry("350x550")
root.resizable(False, False)
root.config(bg="#1e1e1e")

expression = ""

def update_display():
    display_var.set(expression)

def click(event):
    global expression
    text = event.widget.cget("text")

    if text == "=":
        try:
            # Handle % as real calculators: 50 + 10% → 55
            if "%" in expression:
                expression = handle_percent(expression)
            result = str(eval(expression))
            expression = result
            update_display()
        except:
            expression = ""
            display_var.set("Error")

    elif text == "C":
        expression = ""
        update_display()

    elif text == "⌫":
        expression = expression[:-1]
        update_display()

    elif text == "+/-":
        toggle_sign()

    elif text == "%":
        expression += "%"
        update_display()

    else:
        expression += text
        update_display()

def handle_percent(expr):
    import re
    match = re.search(r"([\d\.]+)([\+\-\*/])([\d\.]+)%", expr)
    if match:
        num1 = float(match.group(1))
        op = match.group(2)
        num2 = float(match.group(3))
        percent_value = (num1 * num2) / 100
        return expr.replace(match.group(0), f"{num1}{op}{percent_value}")
    return expr

def toggle_sign():
    global expression
    if expression:
        # If last number is negative, remove "-"
        tokens = list(expression)
        i = len(tokens) - 1
        while i >= 0 and (tokens[i].isdigit() or tokens[i] == '.'):
            i -= 1
        if i >= 0 and tokens[i] == '-':
            tokens.pop(i)
        else:
            tokens.insert(i + 1, '-')
        expression_new = ''.join(tokens)
        expression = expression_new
        update_display()

display_var = tk.StringVar()
display = tk.Entry(root, textvar=display_var, font="Arial 28", bd=0, bg="#1e1e1e", fg="white", justify="right")
display.pack(fill=tk.BOTH, ipadx=8, ipady=20, pady=20, padx=10)

btn_layout = [
    ["C", "⌫", "%", "/"],
    ["7", "8", "9", "*"],
    ["4", "5", "6", "-"],
    ["1", "2", "3", "+"],
    ["+/-", "0", ".", "="]
]

for row in btn_layout:
    frame = tk.Frame(root, bg="#1e1e1e")
    frame.pack(expand=True, fill="both")
    for btn in row:
        color = "#fe9505" if btn in ["=", "/", "*", "-", "+", "%"] else "#2e2e2e"
        b = tk.Button(frame, text=btn, font="Arial 20", bg=color, fg="white", bd=0, relief=tk.FLAT)
        b.pack(side="left", expand=True, fill="both", padx=1, pady=1)
        b.bind("<Button-1>", click)

root.mainloop()
